# --- !Ups

-- ----------------------------
-- 添加CRM跟进阶段字典
-- ----------------------------

-- 添加CRM跟进阶段父级字典
INSERT INTO sys_dictionaries (id, code, dict_name, dict_type, dict_value, dict_label, color, sort, status)
VALUES ('100', 'crm-follow-up-stage', 'CRM跟进阶段', 'crm', '0', 'CRM跟进阶段', '#409EFF', 1, 1);

-- 添加CRM跟进阶段子项
INSERT INTO sys_dictionaries (id, dict_id, code, dict_name, dict_type, dict_value, dict_label, color, sort, status)
VALUES
    ('101', '100', 'crm-follow-up-stage-initial', '初次接触', 'crm', '1', '初次接触', '#67C23A', 1, 1),
    ('102', '100', 'crm-follow-up-stage-requirement', '需求了解', 'crm', '2', '需求了解', '#E6A23C', 2, 1),
    ('103', '100', 'crm-follow-up-stage-proposal', '方案提供', 'crm', '3', '方案提供', '#409EFF', 3, 1),
    ('104', '100', 'crm-follow-up-stage-negotiation', '商务谈判', 'crm', '4', '商务谈判', '#F56C6C', 4, 1),
    ('105', '100', 'crm-follow-up-stage-contract', '合同签署', 'crm', '5', '合同签署', '#909399', 5, 1);

-- 添加CRM跟进状态字典
INSERT INTO sys_dictionaries (id, code, dict_name, dict_type, dict_value, dict_label, color, sort, status)
VALUES ('200', 'crm-follow-up-status', 'CRM跟进状态', 'crm', '0', 'CRM跟进状态', '#409EFF', 2, 1);

-- 添加CRM跟进状态子项
INSERT INTO sys_dictionaries (id, dict_id, code, dict_name, dict_type, dict_value, dict_label, color, sort, status)
VALUES
    ('201', '200', 'crm-follow-up-status-pending', '待跟进', 'crm', '0', '待跟进', '#E6A23C', 1, 1),
    ('202', '200', 'crm-follow-up-status-followed', '已跟进', 'crm', '1', '已跟进', '#67C23A', 2, 1),
    ('203', '200', 'crm-follow-up-status-rejected', '已拒绝', 'crm', '2', '已拒绝', '#F56C6C', 3, 1),
    ('204', '200', 'crm-follow-up-status-closed', '已关闭（退回公海）', 'crm', '3', '已关闭（退回公海）', '#909399', 4, 1),
    ('205', '200', 'crm-follow-up-status-converted', '已转化', 'crm', '4', '已转化', '#67C23A', 5, 1),
    ('206', '200', 'crm-follow-up-status-lost', '已流失', 'crm', '5', '已流失', '#F56C6C', 6, 1);

# --- !Downs

-- 删除CRM相关字典数据
DELETE FROM sys_dictionaries WHERE code LIKE 'crm-follow-up-%';
