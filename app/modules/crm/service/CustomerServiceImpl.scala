package modules.crm.service

import cn.idev.excel.FastExcelFactory
import cn.idev.excel.context.AnalysisContext
import cn.idev.excel.read.listener.ReadListener
import core.AbstractBaseService
import jakarta.inject.Inject
import modules.crm.models.dto.CustomerExcel
import modules.crm.models.{Customer, CustomerTable, Progress}
import modules.crm.repository.{CustomerRepository, ProgressRepository}
import modules.dict.service.DataDictService
import org.slf4j.{Logger, LoggerFactory}

import java.io.InputStream
import java.time.OffsetDateTime
import java.util.concurrent.atomic.AtomicInteger
import scala.collection.mutable.ListBuffer
import scala.concurrent.{ExecutionContext, Future, Promise}
import scala.util.{Failure, Success, Try}


/**
 * 客户服务实现类
 *
 * @param repository 客户仓库
 * @param ec         执行上下文
 */
class CustomerServiceImpl @Inject()(
                                     val dataDictService: DataDictService,
                                     val progressRepository: ProgressRepository,
                                     override val repository: CustomerRepository,
                                   )(implicit override val ec: ExecutionContext)
  extends AbstractBaseService[Customer, CustomerTable, CustomerRepository](repository) with CustomerService {

  private val log: Logger = LoggerFactory.getLogger(classOf[CustomerServiceImpl])

  /**
   * 获取实体类的Class对象
   *
   * @return Customer类的Class对象
   */
  override def getEntityClass: Class[Customer] = classOf[Customer]

  /**
   * 获取未分配销售人员的客户列表
   *
   * @return 未分配销售人员的客户列表
   */
  def getUnassignedCustomers(page: Int, pageSize: Int): Future[(Seq[Customer], Int)] = {
    repository.findUnassigned(page, pageSize)
  }

  /**
   * 获取指定销售人员负责的客户
   *
   * @param salesmanId 销售人员ID
   * @return 该销售人员负责的客户列表
   */
  def getCustomersBySalesman(salesmanId: String, page: Int, pageSize: Int): Future[(Seq[Customer], Int)] = {
    repository.findBySalesman(salesmanId, page, pageSize)
  }

  /**
   * 将客户分配给销售人员
   *
   * @param customerId 客户ID
   * @param salesmanId 销售人员ID
   * @return 分配结果，1表示成功，0表示失败
   */
  def assignCustomerToSalesman(customerId: String, salesmanId: String): Future[Int] = {
    // 先执行客户分配
    repository.assignSalesman(customerId, salesmanId).flatMap { assignResult =>
      if (assignResult > 0) {
        // 分配成功后自动创建跟踪流程
        createFollowUpProcesses(customerId, salesmanId).map { _ =>
          log.info(s"客户分配成功并创建跟踪流程: customerId=$customerId, salesmanId=$salesmanId")
          assignResult
        }.recover {
          case e: Exception =>
            log.error(s"创建跟踪流程失败，但客户分配已成功: customerId=$customerId, salesmanId=$salesmanId", e)
            // 即使创建跟踪流程失败，也返回分配成功的结果
            assignResult
        }
      } else {
        // 分配失败，直接返回结果
        Future.successful(assignResult)
      }
    }
  }

  /**
   * 为客户创建跟踪流程
   *
   * @param customerId 客户ID
   * @param salesmanId 销售人员ID
   * @return 创建结果
   */
  private def createFollowUpProcesses(customerId: String, salesmanId: String): Future[Option[Int]] = {
    // 获取跟进阶段字典数据
    dataDictService.selectOptions(1, 1000, Some("crm-follow-up-stage")).flatMap { case (stages, _) =>
      if (stages.nonEmpty) {
        // 为每个阶段创建一个进度记录
        val progressList = stages.map { stage =>
          Progress(
            customerId = customerId,
            salesmanId = salesmanId,
            phase = stage.dictLabel,
            plan = "待跟进",
            remark = Some(s"系统自动创建的${stage.dictLabel}跟进记录"),
            status = "0", // 默认状态：待跟进
            createdAt = OffsetDateTime.now(),
            updatedAt = OffsetDateTime.now()
          )
        }

        // 批量保存进度记录
        progressRepository.saveAll(progressList).map { result =>
          log.info(s"成功创建${progressList.size}个跟踪流程记录")
          result
        }
      } else {
        log.warn("未找到CRM跟进阶段字典数据，跳过创建跟踪流程")
        Future.successful(Some(0))
      }
    }.recover {
      case e: Exception =>
        log.error("获取跟进阶段字典数据失败", e)
        throw e
    }
  }

  /**
   * 处理Excel文件并导入客户数据
   *
   * @param inputStream Excel文件输入流
   * @return 导入结果元组 (导入成功数, 导入失败数, 总数)
   */
  def processExcelFile(inputStream: InputStream): Future[(Int, Int, Int)] = {
    val promise = Promise[(Int, Int, Int)]()

    try {
      // 用于统计处理结果
      val total = new AtomicInteger(0)
      val validCustomers = new ListBuffer[Customer]()
      val invalidCount = new AtomicInteger(0)

      // 使用fastexcel库读取Excel文件
      FastExcelFactory.read(inputStream, classOf[CustomerExcel], new ReadListener[CustomerExcel] {

        def invoke(data: CustomerExcel, context: AnalysisContext): Unit = {
          total.incrementAndGet()

          try {
            // 验证必填字段
            if (data.company == null || data.company.trim.isEmpty ||
              data.source == null || data.source.trim.isEmpty ||
              data.`type` == null || data.`type`.trim.isEmpty) {
              log.warn(s"Excel行数据缺少必填字段: $data")
              invalidCount.incrementAndGet()
              return
            }

            // 将CustomerExcel对象转换为Customer对象
            val customer = Customer(
              name = data.company.trim,
              linkMan = data.linkMan.trim,
              position = Option(data.position).filter(_.nonEmpty),
              country = Option(data.country).filter(_.nonEmpty),
              address = Option(data.address).filter(_.nonEmpty),
              phone = Option(data.phone).filter(_.nonEmpty),
              mobile = Option(data.mobile).filter(_.nonEmpty),
              website = Option(data.website).filter(_.nonEmpty),
              email = Option(data.email).filter(_.nonEmpty),
              source = data.source.trim,
              type_ = data.`type`.trim,
              description = Option(data.description).filter(_.nonEmpty),
              createdAt = OffsetDateTime.now(),
              updatedAt = OffsetDateTime.now()
            )

            // 添加到有效客户列表
            validCustomers.synchronized {
              validCustomers += customer
            }
          } catch {
            case e: Exception =>
              log.error(s"处理Excel行数据失败: $data", e)
              invalidCount.incrementAndGet()
          }
        }

        def doAfterAllAnalysed(context: AnalysisContext): Unit = {
          log.info(s"Excel读取完成，共${total.get()}条数据，有效${validCustomers.size}条，无效${invalidCount.get()}条")

          // 批量保存有效的客户数据
          if (validCustomers.nonEmpty) {
            saveAll(validCustomers.toSeq).onComplete {
              case Success(_) =>
                promise.success((validCustomers.size, invalidCount.get(), total.get()))
              case Failure(e) =>
                log.error("批量保存客户数据失败", e)
                promise.failure(e)
            }
          } else {
            promise.success((0, invalidCount.get(), total.get()))
          }
        }
      }).sheet.doRead()

    } catch {
      case e: Exception =>
        log.error("处理Excel文件失败", e)
        promise.failure(e)
    } finally {
      // 关闭输入流
      Try(inputStream.close())
    }

    promise.future
  }


  def setCustomerStatus(customerId: String, status: String): Future[Boolean] = {
    Future.successful(true)
  }

  /**
   * 带搜索条件的分页查询客户
   *
   * @param page     页码
   * @param pageSize 每页大小
   * @param search   搜索条件Map
   * @return 分页结果
   */
  def searchCustomers(page: Int, pageSize: Int, search: Map[String, Any]): Future[(Seq[Customer], Int)] = {
    repository.pageWithSearch(page, pageSize, search)
  }
}
