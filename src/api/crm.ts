import { http } from "@/utils/http";
import { baseUrlApi } from "@/api/utils";

type Result = {
  success: boolean;
  data?: Array<any>;
  message?: string;
};

// Alias for Result type to maintain compatibility
type r = Result;

type ResultTable = {
  success: boolean;
  data?: {
    /** 列表数据 */
    list: Array<any>;
    /** 总条目数 */
    total?: number;
    /** 每页显示条目个数 */
    pageSize?: number;
    /** 当前页数 */
    currentPage?: number;
  };
  message?: string;
};

/** 获取公海客户 */
export const getHighSeasCustomers = (params?: object) => {
  const { page, pageSize, searchParams } = (params as any) || {};

  return http.request<ResultTable>(
    "post",
    baseUrlApi(`crm2/customers-unassigned?page=${page}&pageSize=${pageSize}`),
    { data: searchParams }
  );
};

/** 获取我的客户 */
export const getMyCustomers = (data?: object) => {
  const { page, pageSize, userId, searchParams } = (data as any) || {};
  return http.request<ResultTable>(
    "post",
    baseUrlApi(
      `crm2/customers-salesman/${userId}?page=${page}&pageSize=${pageSize}`
    ),
    { data: searchParams }
  );
};

/** 添加客户 */
export const addCustomer = (data?: object) => {
  return http.request<ResultTable>("post", baseUrlApi(`crm2/customers`), {
    data
  });
};

/** 更新客户 */
export const updateCustomer = (data?: object) => {
  return http.request<ResultTable>("put", baseUrlApi(`crm2/customers`), {
    data
  });
};

/** 删除客户 */
export const delCustomer = (id: string) => {
  return http.request<ResultTable>(
    "delete",
    baseUrlApi(`crm2/customers/${id}`),
    {}
  );
};

/** 导入客户 */
export const batchImport = (data: FormData) => {
  return http.request<r>("post", baseUrlApi("crm2/customers/batch-import"), {
    data,
    headers: {
      "Content-Type": "multipart/form-data"
    }
  });
};

/** 选择跟进客户 */
export const followUp = (data: any) => {
  // salesmanId、customerId
  return http.request<r>("post", baseUrlApi(`crm2/customers-assign`), { data });
};

/** 跟踪进度列表数据 */
export const progressList = (data: any) => {
  const { page, pageSize, salesmanId, searchParams } = (data as any) || {};
  return http.request<r>(
    "post",
    baseUrlApi(
      `crm2/progress?salesmanId=${salesmanId}&page=${page}&pageSize=${pageSize}`
    ),
    { data: searchParams }
  );
};

/** 添加跟踪进度 */
export const addProgress = (data?: object) => {
  return http.request<ResultTable>("post", baseUrlApi(`crm2/progress-add`), {
    data
  });
};

/** 更新跟踪进度 */
export const updateProgress = (data?: object) => {
  return http.request<ResultTable>("put", baseUrlApi(`crm2/progress-update`), {
    data
  });
};

/** 删除跟踪进度 */
export const delProgress = (id: string) => {
  return http.request<ResultTable>(
    "delete",
    baseUrlApi(`crm2/progress/${id}`),
    {}
  );
};

type StatusChangeData = {
  userId: string;
  customerId: string;
  progressId: string;
  status: string; // 状态，字典里有（0-待跟进 1-已跟进 2-已拒绝 3-已关闭（退回公海） 4-已转化 5-已流失）
};

/** 客户跟踪状态更改 */
export const progressStatusChange = (data: StatusChangeData) => {
  return http.request<Result>("post", baseUrlApi(`crm2/progress-status`), {
    data
  });
};

/** 客户退回公海池 */
export const customerReturnToPool = (data: object) => {
  // 只需要customerId和status
  return http.request<Result>("post", baseUrlApi(`crm2/customers/set-status`), {
    data
  });
};
